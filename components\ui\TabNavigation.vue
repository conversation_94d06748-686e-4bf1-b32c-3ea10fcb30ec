<template>
  <div class="flex flex-wrap items-center text-sm overflow-hidden">
    <transition name="modal-fade">
      <div class="w-full transition-all duration-300 transform overflow-hidden">
        <div
          ref="tabContainer"
          class="relative flex overflow-x-auto scrollbar-hide"
        >
          <button
            v-for="(tab, index) in tabs"
            :key="tab.value"
            ref="tabButtons"
            @click="handleClickTab(tab, index)"
            :class="getTabClass(tab)"
            class="h-8 px-4 text-center relative transition-all duration-300 flex items-center justify-center whitespace-nowrap flex-shrink-0"
          >
            <!-- Icon (if provided) -->
            <component v-if="tab.icon" :is="tab.icon" class="w-4 h-4 mr-2" />
            {{ tab.label }}
          </button>

          <!-- Thanh gạch chân có animation -->
          <div
            class="absolute bottom-0 h-1 bg-primary transition-transform duration-300 ease-in-out"
            :style="{
              width: `${underlineWidth}px`,
              transform: `translateX(${underlineLeft}px)`,
            }"
          ></div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, watch } from "vue";

// Types
interface Tab {
  value: string;
  label: string;
  icon?: any;
  disabled?: boolean;
}

// Props
interface Props {
  tabs: Tab[];
  modelValue?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "pills" | "underline";
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  size: "md",
  variant: "underline",
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: string];
  "tab-change": [tab: Tab];
}>();

// Reactive state
const selectedTab = ref<string>(props.modelValue);
const underlineLeft = ref(0);
const underlineWidth = ref(0);
const tabButtons = ref<HTMLElement[]>([]);
const tabContainer = ref<HTMLElement>();

// Computed
const getTabClass = (tab: Tab) => {
  const baseClass = "";

  const isActive = selectedTab.value === tab.value;
  const activeClass = "text-primary font-medium";
  const inactiveClass = "text-gray-600 hover:text-gray-900";
  const disabledClass = "text-gray-400 cursor-not-allowed";

  let classes = baseClass;

  if (tab.disabled) {
    classes += ` ${disabledClass}`;
  } else if (isActive) {
    classes += ` ${activeClass}`;
  } else {
    classes += ` ${inactiveClass}`;
  }

  return classes;
};

// Methods
const handleClickTab = async (tab: Tab, index: number) => {
  if (tab.disabled) return;

  selectedTab.value = tab.value;
  emit("update:modelValue", tab.value);
  emit("tab-change", tab);

  await nextTick();
  updateUnderline(index);
};

const updateUnderline = (index: number) => {
  if (tabButtons.value[index]) {
    const tabElement = tabButtons.value[index];
    underlineLeft.value = tabElement.offsetLeft;
    underlineWidth.value = tabElement.offsetWidth;

    // Auto scroll to selected tab if it's out of view
    scrollToTab(tabElement);
  }
};

const scrollToTab = (tabElement: HTMLElement) => {
  const container = tabContainer.value;
  if (!container || !tabElement) return;

  const containerRect = container.getBoundingClientRect();
  const tabRect = tabElement.getBoundingClientRect();

  // Check if tab is out of view
  if (
    tabRect.right > containerRect.right ||
    tabRect.left < containerRect.left
  ) {
    // Calculate scroll position to center the tab
    const scrollLeft =
      tabElement.offsetLeft -
      container.clientWidth / 2 +
      tabElement.offsetWidth / 2;
    container.scrollTo({
      left: Math.max(0, scrollLeft),
      behavior: "smooth",
    });
  }
};

const initializeTab = async () => {
  await nextTick();

  if (tabButtons.value.length > 0) {
    // Find the active tab index
    const activeIndex = props.tabs.findIndex(
      (tab) => tab.value === selectedTab.value
    );

    if (activeIndex >= 0) {
      updateUnderline(activeIndex);
    } else {
      // Default to first tab if no active tab found
      const firstEnabledTab = props.tabs.find((tab) => !tab.disabled);
      if (firstEnabledTab) {
        selectedTab.value = firstEnabledTab.value;
        emit("update:modelValue", firstEnabledTab.value);
        emit("tab-change", firstEnabledTab);
        updateUnderline(0);
      }
    }
  }
};

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== selectedTab.value) {
      selectedTab.value = newValue;
      const activeIndex = props.tabs.findIndex((tab) => tab.value === newValue);
      if (activeIndex >= 0) {
        updateUnderline(activeIndex);
      }
    }
  }
);

// Watch for tabs changes
watch(
  () => props.tabs,
  () => {
    initializeTab();
  },
  { deep: true }
);

// Lifecycle
onMounted(() => {
  initializeTab();
});

// Expose methods for external control
defineExpose({
  setActiveTab: (value: string) => {
    const index = props.tabs.findIndex((tab) => tab.value === value);
    if (index >= 0) {
      handleClickTab(props.tabs[index], index);
    }
  },
  getActiveTab: () => selectedTab.value,
});
</script>

<style scoped>
/* Hiệu ứng modal */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Smooth scrolling */
.scrollbar-hide {
  scroll-behavior: smooth;
}

/* Ensure container doesn't overflow */
.overflow-hidden {
  overflow: hidden !important;
}

/* Prevent horizontal scroll on parent elements */
.flex-wrap {
  flex-wrap: nowrap !important;
}

/* Disabled state */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .h-8 {
    height: 1.75rem;
  }
}
</style>
