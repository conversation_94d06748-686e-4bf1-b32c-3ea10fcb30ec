<template>
  <div class="bg-white rounded-lg border border-gray-200 md:h-screen-50">
    <div class="p-2 space-y-2">
      <!-- Custom Date Range -->
      <div>
        <div class="flex items-center gap-2">
          <div class="w-1/2">
            <label class="block text-sm text-gray-700 mb-1 font-medium"
              >Từ ngày</label
            >
            <input
              v-model="filters.dateFrom"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
          <div class="w-1/2">
            <label class="block text-sm text-gray-700 mb-1 font-medium"
              ><PERSON><PERSON><PERSON> ngày</label
            >
            <input
              v-model="filters.dateTo"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
        </div>
      </div>

      <!-- Employee Select -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Chọn nhân viên
        </label>
        <div class="relative">
          <button
            @click="toggleEmployeeDropdown"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm text-left bg-white flex items-center justify-between"
          >
            <span
              v-if="filters.selectedEmployees.length === 0"
              class="text-gray-500"
            >
              Chọn nhân viên...
            </span>
            <span v-else class="text-gray-900">
              {{ getSelectedEmployeesText() }}
            </span>
            <svg
              :class="[
                'w-4 h-4 text-gray-400 transition-transform',
                isEmployeeDropdownOpen ? 'rotate-180' : '',
              ]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          <!-- Dropdown -->
          <div
            v-if="isEmployeeDropdownOpen"
            class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-64 overflow-hidden"
          >
            <!-- Search Input -->
            <div class="p-3 border-b border-gray-200">
              <div class="relative">
                <input
                  v-model="searchEmployee"
                  type="text"
                  placeholder="Tìm kiếm nhân viên..."
                  class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                  @input="handleEmployeeSearch"
                />
                <svg
                  class="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            <!-- Options -->
            <div class="max-h-48 overflow-y-auto">
              <div v-if="isLoadingEmployees" class="p-4 text-center">
                <div
                  class="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full mx-auto"
                ></div>
                <p class="text-sm text-gray-500 mt-2">Đang tải...</p>
              </div>

              <div
                v-else-if="filteredEmployees.length === 0"
                class="p-4 text-center text-gray-500 text-sm"
              >
                {{
                  searchEmployee
                    ? "Không tìm thấy nhân viên"
                    : "Chưa có nhân viên"
                }}
              </div>

              <div v-else>
                <!-- Select All Option -->
                <label
                  class="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                >
                  <input
                    :checked="isAllEmployeesSelected"
                    @change="toggleAllEmployees"
                    type="checkbox"
                    class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
                  />
                  <span class="ml-3 text-sm font-medium text-gray-900">
                    Chọn tất cả ({{ filteredEmployees.length }})
                  </span>
                </label>

                <!-- Employee Options -->
                <label
                  v-for="employee in filteredEmployees"
                  :key="employee.id"
                  class="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <input
                    v-model="filters.selectedEmployees"
                    :value="employee.id"
                    type="checkbox"
                    class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
                  />
                  <div class="ml-3 flex items-center gap-3 flex-1">
                    <img
                      :src="employee.avatar || 'https://placehold.co/32'"
                      :alt="employee.name"
                      class="w-8 h-8 rounded-full object-cover border border-gray-200"
                    />
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ employee.name }}
                      </p>
                      <p class="text-xs text-gray-500 truncate">
                        {{ employee.code }} • {{ employee.department }}
                      </p>
                    </div>
                    <div class="flex items-center gap-1">
                      <div
                        :class="getEmployeeStatusClass(employee.status)"
                        class="w-2 h-2 rounded-full"
                      ></div>
                      <span class="text-xs text-gray-500">
                        {{ getEmployeeStatusText(employee.status) }}
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Footer Actions -->
            <div class="p-3 border-t border-gray-200 bg-gray-50">
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-500">
                  {{ filters.selectedEmployees.length }} nhân viên đã chọn
                </span>
                <div class="flex gap-2">
                  <button
                    @click="clearSelectedEmployees"
                    class="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Xóa tất cả
                  </button>
                  <button
                    @click="closeEmployeeDropdown"
                    class="text-xs bg-primary text-white px-3 py-1 rounded hover:bg-primary/90"
                  >
                    Xong
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- task -->
      <div class="border"></div>
      <div class="flex items-center justify-between">
        <label class="font-semibold text-sm">Danh sách công việc</label>
        <div class="bg-primary text-white px-2 py-1 rounded flex items-center cursor-pointer">
          <span
            ><svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="size-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
          </span>
          <span>Tạo công việc</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from "vue";

// Types
interface Employee {
  id: string;
  name: string;
  code: string;
  avatar?: string;
  status: "active" | "inactive" | "on_leave";
  department?: string;
}

interface QuickPeriod {
  value: string;
  label: string;
  days?: number;
}

interface StatusOption {
  value: string;
  label: string;
  colorClass: string;
}

interface Filters {
  dateFrom: string;
  dateTo: string;
  selectedEmployees: string[];
  selectedStatuses: string[];
}

// Emits
const emit = defineEmits<{
  filtersChanged: [filters: Filters & { period: string }];
  employeeSelected: [employees: Employee[]];
  dateRangeChanged: [dateFrom: string, dateTo: string];
}>();

// Reactive state
const selectedPeriod = ref<string>("today");
const searchEmployee = ref<string>("");
const isLoadingEmployees = ref<boolean>(false);
const isApplying = ref<boolean>(false);
const isEmployeeDropdownOpen = ref<boolean>(false);

const filters = reactive<Filters>({
  dateFrom: "",
  dateTo: "",
  selectedEmployees: [],
  selectedStatuses: [],
});

// Quick period options
const quickPeriods: QuickPeriod[] = [
  { value: "today", label: "Hôm nay" },
  { value: "yesterday", label: "Hôm qua" },
  { value: "this_week", label: "Tuần này" },
  { value: "last_week", label: "Tuần trước" },
  { value: "this_month", label: "Tháng này" },
  { value: "last_month", label: "Tháng trước" },
  { value: "last_7_days", label: "7 ngày qua" },
  { value: "custom", label: "Tùy chọn" },
];

// Status options
const statusOptions: StatusOption[] = [
  {
    value: "CHECK_IN",
    label: "Vào ca",
    colorClass: "bg-green-100 text-green-800",
  },
  {
    value: "CHECK_OUT",
    label: "Hết ca",
    colorClass: "bg-red-100 text-red-800",
  },
];

// Mock employees data
const mockEmployees: Employee[] = [
  {
    id: "1",
    name: "Nguyễn Văn A",
    code: "NV001",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Kinh doanh",
  },
  {
    id: "2",
    name: "Trần Thị B",
    code: "NV002",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Marketing",
  },
  {
    id: "3",
    name: "Lê Văn C",
    code: "NV003",
    avatar: "https://placehold.co/32",
    status: "on_leave",
    department: "IT",
  },
  {
    id: "4",
    name: "Phạm Thị D",
    code: "NV004",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Nhân sự",
  },
  {
    id: "5",
    name: "Hoàng Văn E",
    code: "NV005",
    avatar: "https://placehold.co/32",
    status: "inactive",
    department: "Kế toán",
  },
  {
    id: "6",
    name: "Vũ Thị F",
    code: "NV006",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Kinh doanh",
  },
  {
    id: "7",
    name: "Đặng Văn G",
    code: "NV007",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Vận hành",
  },
  {
    id: "8",
    name: "Bùi Thị H",
    code: "NV008",
    avatar: "https://placehold.co/32",
    status: "on_leave",
    department: "Marketing",
  },
];

const allEmployees = ref<Employee[]>(mockEmployees);

// Computed properties
const filteredEmployees = computed(() => {
  if (!searchEmployee.value) {
    return allEmployees.value;
  }

  const search = searchEmployee.value.toLowerCase();
  return allEmployees.value.filter(
    (employee) =>
      employee.name.toLowerCase().includes(search) ||
      employee.code.toLowerCase().includes(search) ||
      employee.department?.toLowerCase().includes(search)
  );
});

const hasActiveFilters = computed(() => {
  return (
    selectedPeriod.value !== "today" ||
    filters.dateFrom ||
    filters.dateTo ||
    filters.selectedEmployees.length > 0 ||
    filters.selectedStatuses.length > 0
  );
});

const isAllEmployeesSelected = computed(() => {
  return (
    filteredEmployees.value.length > 0 &&
    filteredEmployees.value.every((emp) =>
      filters.selectedEmployees.includes(emp.id)
    )
  );
});

// Methods
const selectQuickPeriod = (period: string) => {
  selectedPeriod.value = period;

  if (period !== "custom") {
    const { dateFrom, dateTo } = getDateRangeForPeriod(period);
    filters.dateFrom = dateFrom;
    filters.dateTo = dateTo;
  }

  // Auto apply for quick periods
  if (period !== "custom") {
    applyFilters();
  }
};

const getDateRangeForPeriod = (
  period: string
): { dateFrom: string; dateTo: string } => {
  const today = new Date();
  const formatDate = (date: Date) => date.toISOString().split("T")[0];

  switch (period) {
    case "today":
      return { dateFrom: formatDate(today), dateTo: formatDate(today) };

    case "yesterday":
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return { dateFrom: formatDate(yesterday), dateTo: formatDate(yesterday) };

    case "this_week":
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay() + 1);
      return { dateFrom: formatDate(startOfWeek), dateTo: formatDate(today) };

    case "last_week":
      const lastWeekStart = new Date(today);
      lastWeekStart.setDate(today.getDate() - today.getDay() - 6);
      const lastWeekEnd = new Date(today);
      lastWeekEnd.setDate(today.getDate() - today.getDay());
      return {
        dateFrom: formatDate(lastWeekStart),
        dateTo: formatDate(lastWeekEnd),
      };

    case "this_month":
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      return { dateFrom: formatDate(startOfMonth), dateTo: formatDate(today) };

    case "last_month":
      const lastMonthStart = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        1
      );
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      return {
        dateFrom: formatDate(lastMonthStart),
        dateTo: formatDate(lastMonthEnd),
      };

    case "last_7_days":
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 7);
      return { dateFrom: formatDate(sevenDaysAgo), dateTo: formatDate(today) };

    default:
      return { dateFrom: "", dateTo: "" };
  }
};

const handleEmployeeSearch = () => {
  // Debounce search if needed
  // For now, just trigger reactive update
};

const getEmployeeStatusClass = (status: string): string => {
  switch (status) {
    case "active":
      return "bg-green-500";
    case "inactive":
      return "bg-gray-400";
    case "on_leave":
      return "bg-yellow-500";
    default:
      return "bg-gray-400";
  }
};

const getEmployeeStatusText = (status: string): string => {
  switch (status) {
    case "active":
      return "Hoạt động";
    case "inactive":
      return "Không hoạt động";
    case "on_leave":
      return "Nghỉ phép";
    default:
      return "Không xác định";
  }
};

const getSelectedPeriodLabel = (): string => {
  const period = quickPeriods.find((p) => p.value === selectedPeriod.value);
  return period?.label || "";
};

// Employee dropdown methods
const toggleEmployeeDropdown = () => {
  isEmployeeDropdownOpen.value = !isEmployeeDropdownOpen.value;
};

const closeEmployeeDropdown = () => {
  isEmployeeDropdownOpen.value = false;
};

const getSelectedEmployeesText = (): string => {
  const count = filters.selectedEmployees.length;
  if (count === 0) return "";
  if (count === 1) {
    const employee = allEmployees.value.find(
      (emp) => emp.id === filters.selectedEmployees[0]
    );
    return employee?.name || "";
  }
  return `${count} nhân viên đã chọn`;
};

const toggleAllEmployees = () => {
  if (isAllEmployeesSelected.value) {
    // Unselect all filtered employees
    const filteredIds = filteredEmployees.value.map((emp) => emp.id);
    filters.selectedEmployees = filters.selectedEmployees.filter(
      (id) => !filteredIds.includes(id)
    );
  } else {
    // Select all filtered employees
    const filteredIds = filteredEmployees.value.map((emp) => emp.id);
    const newSelected = [
      ...new Set([...filters.selectedEmployees, ...filteredIds]),
    ];
    filters.selectedEmployees = newSelected;
  }
};

const clearSelectedEmployees = () => {
  filters.selectedEmployees = [];
};

const applyFilters = async () => {
  isApplying.value = true;

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500));

    const filterData = {
      ...filters,
      period: selectedPeriod.value,
    };

    emit("filtersChanged", filterData);
    emit("dateRangeChanged", filters.dateFrom, filters.dateTo);

    if (filters.selectedEmployees.length > 0) {
      const selectedEmployeeObjects = allEmployees.value.filter((emp) =>
        filters.selectedEmployees.includes(emp.id)
      );
      emit("employeeSelected", selectedEmployeeObjects);
    }
  } catch (error) {
    console.error("Error applying filters:", error);
  } finally {
    isApplying.value = false;
  }
};

const clearFilters = () => {
  selectedPeriod.value = "today";
  filters.dateFrom = "";
  filters.dateTo = "";
  filters.selectedEmployees = [];
  filters.selectedStatuses = [];
  searchEmployee.value = "";

  // Auto apply after clearing
  applyFilters();
};

// Initialize with today's data
onMounted(() => {
  selectQuickPeriod("today");

  // Add click outside listener for dropdown
  document.addEventListener("click", handleClickOutside);
});

// Cleanup
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

// Click outside handler
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const dropdown = target.closest(".relative");
  if (!dropdown && isEmployeeDropdownOpen.value) {
    isEmployeeDropdownOpen.value = false;
  }
};

// Watch for date changes to update period
watch([() => filters.dateFrom, () => filters.dateTo], () => {
  if (filters.dateFrom || filters.dateTo) {
    selectedPeriod.value = "custom";
  }
});
</script>
