<template>
  <div class="ckeditor-wrapper">
    <div ref="editorElement" class="ckeditor-container"></div>

    <!-- Character Count -->
    <div
      v-if="showCharacterCount && characterCount > 0"
      class="character-count"
    >
      {{ characterCount }} characters
    </div>
  </div>
</template>

<script setup>
import { ClassicEditor } from "ckeditor5";

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "Nhập nội dung...",
  },
  showCharacterCount: {
    type: Boolean,
    default: false,
  },
  editable: {
    type: Boolean,
    default: true,
  },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

// Reactive data
const editorElement = ref(null);
const editorInstance = ref(null);
const characterCount = ref(0);

// CKEditor configuration
const editorConfig = {
  placeholder: props.placeholder,
  toolbar: {
    items: [
      "undo",
      "redo",
      "|",
      "heading",
      "|",
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "|",
      "bulletedList",
      "numberedList",
      "|",
      "blockQuote",
      "insertTable",
      "|",
      "link",
      "mediaEmbed",
      "|",
      "outdent",
      "indent",
      "|",
      "removeFormat",
    ],
    shouldNotGroupWhenFull: false,
  },
  heading: {
    options: [
      { model: "paragraph", title: "Paragraph", class: "ck-heading_paragraph" },
      {
        model: "heading1",
        view: "h1",
        title: "Heading 1",
        class: "ck-heading_heading1",
      },
      {
        model: "heading2",
        view: "h2",
        title: "Heading 2",
        class: "ck-heading_heading2",
      },
      {
        model: "heading3",
        view: "h3",
        title: "Heading 3",
        class: "ck-heading_heading3",
      },
    ],
  },
  table: {
    contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
  },
  link: {
    addTargetToExternalLinks: true,
    defaultProtocol: "https://",
  },
};

// Initialize editor
const initializeEditor = async () => {
  if (!editorElement.value) return;

  try {
    const editor = await ClassicEditor.create(
      editorElement.value,
      editorConfig
    );

    editorInstance.value = editor;

    // Set initial content
    if (props.modelValue) {
      editor.setData(props.modelValue);
    }

    // Set editable state
    editor.isReadOnly = !props.editable;

    // Listen for content changes
    editor.model.document.on("change:data", () => {
      const data = editor.getData();
      emit("update:modelValue", data);

      // Update character count
      if (props.showCharacterCount) {
        const text =
          editor.editing.view.document.getRoot().getChild(0)?.getChild(0)
            ?.data || "";
        characterCount.value = text.length;
      }
    });
  } catch (error) {
    console.error("Error initializing CKEditor:", error);
  }
};

// Watch for prop changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (editorInstance.value && editorInstance.value.getData() !== newValue) {
      editorInstance.value.setData(newValue || "");
    }
  }
);

watch(
  () => props.editable,
  (newValue) => {
    if (editorInstance.value) {
      editorInstance.value.isReadOnly = !newValue;
    }
  }
);

// Lifecycle hooks
onMounted(() => {
  nextTick(() => {
    initializeEditor();
  });
});

onBeforeUnmount(() => {
  if (editorInstance.value) {
    editorInstance.value.destroy();
    editorInstance.value = null;
  }
});
</script>

<style scoped>
.ckeditor-wrapper {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.ckeditor-container {
  min-height: 200px;
}

.character-count {
  padding: 8px 12px;
  font-size: 12px;
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  text-align: right;
}

/* CKEditor 5 custom styling */
:deep(.ck-editor) {
  border: none !important;
}

:deep(.ck-editor__top) {
  border-bottom: 1px solid #e5e7eb !important;
  background: #f9fafb !important;
}

:deep(.ck-toolbar) {
  border: none !important;
  background: transparent !important;
  padding: 8px 12px !important;
}

:deep(.ck-toolbar__separator) {
  background: #d1d5db !important;
  width: 1px !important;
  margin: 0 4px !important;
}

:deep(.ck-button) {
  border-radius: 4px !important;
  margin: 0 2px !important;
  transition: all 0.2s ease !important;
}

:deep(.ck-button:not(.ck-disabled):hover) {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
}

:deep(.ck-button.ck-on) {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
}

:deep(.ck-button.ck-on:hover) {
  background: #2563eb !important;
  border-color: #2563eb !important;
}

:deep(.ck-editor__main) {
  border: none !important;
}

:deep(.ck-content) {
  padding: 16px !important;
  min-height: 200px !important;
  border: none !important;
  font-family: inherit !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

:deep(.ck-content:focus) {
  outline: none !important;
  box-shadow: none !important;
}

/* Content styling */
:deep(.ck-content h1) {
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}

:deep(.ck-content h2) {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.75em 0 !important;
}

:deep(.ck-content h3) {
  font-size: 1.17em !important;
  font-weight: bold !important;
  margin: 1em 0 !important;
}

:deep(.ck-content ul, .ck-content ol) {
  padding-left: 1.5em !important;
  margin: 1em 0 !important;
}

:deep(.ck-content blockquote) {
  border-left: 4px solid #e5e7eb !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  font-style: italic !important;
  color: #6b7280 !important;
}

:deep(.ck-content table) {
  border-collapse: collapse !important;
  margin: 1em 0 !important;
  width: 100% !important;
}

:deep(.ck-content table td, .ck-content table th) {
  border: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
}

:deep(.ck-content table th) {
  background: #f9fafb !important;
  font-weight: bold !important;
}

:deep(.ck-content a) {
  color: #3b82f6 !important;
  text-decoration: underline !important;
}

:deep(.ck-content a:hover) {
  color: #2563eb !important;
}

/* Placeholder styling */
:deep(.ck-placeholder::before) {
  color: #9ca3af !important;
  font-style: normal !important;
}

/* Dropdown styling */
:deep(.ck-dropdown__panel) {
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

:deep(.ck-list__item) {
  border-radius: 4px !important;
  margin: 2px !important;
}

:deep(.ck-list__item:hover) {
  background: #f3f4f6 !important;
}

:deep(.ck-list__item.ck-on) {
  background: #3b82f6 !important;
  color: white !important;
}
</style>
