<template>
  <div class="h-full flex flex-col p-4 space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">
        Nội dung công việc hôm nay
      </h3>
      <button
        class="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors duration-200"
      >
        <PlusIcon class="w-4 h-4" />
        Thêm công việc
      </button>
    </div>
    <CKEditor
      v-model="workContent"
      placeholder="Nhập nội dung công việc hôm nay..."
      :show-character-count="true"
    />
  </div>
</template>

<script setup lang="ts">
// Reactive data for work content
const workContent = ref("<p>Bắt đ<PERSON><PERSON> nhập nội dung công việc...</p>");

// Handle content changes
watch(workContent, (newContent) => {
  console.log("Work content updated:", newContent);
  // Here you can save to API or local storage
});
</script>
